# PDF Annotation App - Manual Testing Guide

## Test Checklist

### ✅ Basic Setup
- [x] Application starts without errors
- [x] UI loads correctly with toolbar and canvas area
- [x] All buttons are visible and properly styled

### 📄 PDF Upload & Display
- [ ] Upload PDF button works
- [ ] PDF file selection dialog opens
- [ ] PDF loads and displays on canvas
- [ ] Page navigation works (Previous/Next buttons)
- [ ] Page counter shows correct information

### 🔍 Zoom Functionality
- [ ] Zoom In button increases canvas size
- [ ] Zoom Out button decreases canvas size
- [ ] Zoom percentage displays correctly
- [ ] <PERSON><PERSON> maintains aspect ratio during zoom

### 📐 Rectangle Annotations
- [ ] Rectangle tool can be selected
- [ ] Click and drag creates rectangle annotation
- [ ] Rectangle appears in annotations list
- [ ] Rectangle coordinates are preserved during zoom
- [ ] Multiple rectangles can be created

### 🔺 Polygon Annotations
- [ ] Polygon tool can be selected
- [ ] Clicking creates polygon points
- [ ] "Finish Polygon" button completes polygon
- [ ] Polygon appears in annotations list
- [ ] Polygon coordinates are preserved during zoom

### 📋 Copy & Paste
- [ ] Copy button works for annotations
- [ ] Right-click paste functionality works
- [ ] Pasted annotations appear at correct location
- [ ] Pasted annotations have unique IDs

### 🗑️ Delete Functionality
- [ ] Delete button removes annotations
- [ ] Annotations list updates correctly
- [ ] Canvas redraws without deleted annotations

### 💾 Export Functionality
- [ ] Save button downloads JSON file
- [ ] Ctrl+S keyboard shortcut works
- [ ] JSON contains correct annotation data
- [ ] PDF dimensions are included in export
- [ ] All annotation coordinates are accurate

### 🔄 Multi-page Support
- [ ] Annotations are page-specific
- [ ] Switching pages shows correct annotations
- [ ] Annotations persist when returning to page

## Test Scenarios

### Scenario 1: Basic Rectangle Annotation
1. Upload test_document.pdf
2. Select Rectangle tool
3. Draw a rectangle on the first page
4. Verify it appears in annotations list
5. Export and check JSON coordinates

### Scenario 2: Polygon Creation
1. Select Polygon tool
2. Click 4-5 points to create a polygon
3. Click "Finish Polygon"
4. Verify polygon in annotations list

### Scenario 3: Copy & Paste
1. Create a rectangle annotation
2. Click "Copy" button
3. Right-click elsewhere on canvas
4. Verify new annotation appears

### Scenario 4: Multi-page Testing
1. Navigate to page 2
2. Create annotations on page 2
3. Switch back to page 1
4. Verify page 1 annotations still exist
5. Export and verify page indices in JSON

### Scenario 5: Zoom & Coordinate Preservation
1. Create annotations at 100% zoom
2. Zoom in to 200%
3. Create more annotations
4. Export and verify all coordinates are in PDF space

## Expected JSON Output Format

```json
{
  "pdfDimensions": {
    "width": 612,
    "height": 792
  },
  "annotations": [
    {
      "id": "uuid-string",
      "type": "rectangle",
      "pageIndex": 0,
      "coordinates": {
        "x": 100,
        "y": 200,
        "width": 150,
        "height": 100,
        "normalized": {
          "x": 0.163,
          "y": 0.253,
          "width": 0.245,
          "height": 0.126
        }
      },
      "color": "#ff0000"
    }
  ]
}
```

## Known Limitations
- PDF.js worker loads from CDN (requires internet connection)
- Large PDF files may take time to process
- Browser memory usage increases with PDF size and annotation count
