<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spatial Room Sorting Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .room-list {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .room-item {
            padding: 5px;
            margin: 2px 0;
            background: white;
            border-radius: 3px;
            display: flex;
            justify-content: space-between;
        }
        .nearby { background-color: #e3f2fd; border-left: 3px solid #2196f3; }
        .distance { font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <h1>🎯 Spatial Room Sorting Test Suite</h1>
    
    <div class="test-container">
        <h2>Test Functions</h2>
        <div id="function-tests"></div>
    </div>

    <div class="test-container">
        <h2>Spatial Sorting Simulation</h2>
        <div id="sorting-tests"></div>
    </div>

    <div class="test-container">
        <h2>Usage Instructions</h2>
        <ol>
            <li><strong>Upload PDF:</strong> Load a floor plan document</li>
            <li><strong>Upload CSV:</strong> Load room data with codes like "OFFICE 01.E.15"</li>
            <li><strong>Create Annotation:</strong> Draw a rectangle or polygon on the PDF</li>
            <li><strong>Observe Sorting:</strong> Room dropdown should show nearest rooms first</li>
            <li><strong>Visual Indicators:</strong> Look for 📍 icons and "Very Close" badges</li>
        </ol>
        
        <h3>Expected Room Code Format</h3>
        <div class="room-list">
            <div class="room-item">OFFICE 01.E.15 <span class="distance">Floor 1, Zone E, Room 15</span></div>
            <div class="room-item">STORAGE 02.A.08 <span class="distance">Floor 2, Zone A, Room 8</span></div>
            <div class="room-item">LAB 01.C.22 <span class="distance">Floor 1, Zone C, Room 22</span></div>
        </div>
    </div>

    <script>
        // Replicate the spatial sorting functions for testing
        function extractRoomCode(roomName) {
            const match = roomName.match(/\s+([0-9]+\.[A-Z]+\.[0-9]+)$/);
            return match ? match[1] : null;
        }

        function parseRoomCode(roomCode) {
            if (!roomCode) return null;
            const parts = roomCode.split('.');
            if (parts.length !== 3) return null;
            
            return {
                floor: parseInt(parts[0], 10) || 0,
                zone: parts[1],
                room: parseInt(parts[2], 10) || 0
            };
        }

        function estimateRoomCoordinates(roomCode) {
            const parsed = parseRoomCode(roomCode);
            if (!parsed) return null;

            const zoneMapping = {
                'A': { x: 0, y: 0 },     // Northwest
                'B': { x: 1, y: 0 },     // Northeast  
                'C': { x: 2, y: 0 },     // Far Northeast
                'D': { x: 0, y: 1 },     // West
                'E': { x: 1, y: 1 },     // Center
                'F': { x: 2, y: 1 },     // East
                'G': { x: 0, y: 2 },     // Southwest
                'H': { x: 1, y: 2 },     // South
                'I': { x: 2, y: 2 },     // Southeast
            };

            const zoneCoords = zoneMapping[parsed.zone] || { x: 1, y: 1 };
            const floorScale = 1000;
            const zoneScale = 300;
            const roomScale = 10;

            return {
                x: (zoneCoords.x * zoneScale) + (parsed.room * roomScale),
                y: (parsed.floor * floorScale) + (zoneCoords.y * zoneScale)
            };
        }

        function calculateDistance(point1, point2) {
            if (!point1 || !point2) return Infinity;
            const dx = point1.x - point2.x;
            const dy = point1.y - point2.y;
            return Math.sqrt(dx * dx + dy * dy);
        }

        // Test functions
        function runTests() {
            const functionTests = document.getElementById('function-tests');
            const sortingTests = document.getElementById('sorting-tests');

            // Test room code extraction
            const extractTests = [
                { input: "OFFICE 01.E.15", expected: "01.E.15" },
                { input: "BIOWASTE 02.A.28", expected: "02.A.28" },
                { input: "STORAGE ROOM", expected: null },
                { input: "LAB 03.C.05", expected: "03.C.05" }
            ];

            functionTests.innerHTML = '<h3>Room Code Extraction Tests</h3>';
            extractTests.forEach(test => {
                const result = extractRoomCode(test.input);
                const passed = result === test.expected;
                functionTests.innerHTML += `
                    <div class="test-result ${passed ? 'pass' : 'fail'}">
                        ${passed ? '✅' : '❌'} "${test.input}" → ${result} 
                        ${passed ? '' : `(expected: ${test.expected})`}
                    </div>
                `;
            });

            // Test coordinate estimation
            functionTests.innerHTML += '<h3>Coordinate Estimation Tests</h3>';
            const coordTests = ["01.E.15", "02.A.08", "01.C.22"];
            coordTests.forEach(code => {
                const coords = estimateRoomCoordinates(code);
                const parsed = parseRoomCode(code);
                functionTests.innerHTML += `
                    <div class="test-result pass">
                        ✅ ${code} → Floor ${parsed.floor}, Zone ${parsed.zone}, Room ${parsed.room} 
                        → Coords (${coords.x}, ${coords.y})
                    </div>
                `;
            });

            // Test spatial sorting simulation
            const sampleRooms = [
                "OFFICE 01.A.10", "STORAGE 01.E.15", "LAB 01.C.20", 
                "MEETING 02.E.12", "OFFICE 01.E.18", "STORAGE 01.B.05"
            ];
            
            const annotationCenter = { x: 315, y: 1300 }; // Near zone E, floor 1
            
            const sortedRooms = sampleRooms.map(roomName => {
                const roomCode = extractRoomCode(roomName);
                const roomCoords = estimateRoomCoordinates(roomCode);
                const distance = calculateDistance(annotationCenter, roomCoords);
                return { name: roomName, code: roomCode, distance, coords: roomCoords };
            }).sort((a, b) => a.distance - b.distance);

            sortingTests.innerHTML = `
                <h3>Spatial Sorting Simulation</h3>
                <p><strong>Annotation Center:</strong> (${annotationCenter.x}, ${annotationCenter.y}) - Near Zone E, Floor 1</p>
                <div class="room-list">
            `;

            sortedRooms.forEach((room, index) => {
                const isNearby = room.distance < 500;
                const isVeryClose = room.distance < 200;
                sortingTests.innerHTML += `
                    <div class="room-item ${isNearby ? 'nearby' : ''}">
                        <span>${index + 1}. ${room.name}</span>
                        <span class="distance">
                            ${Math.round(room.distance)} units
                            ${isVeryClose ? ' 🏆 Very Close' : isNearby ? ' 📍 Nearby' : ''}
                        </span>
                    </div>
                `;
            });

            sortingTests.innerHTML += '</div>';

            // Verify sorting logic
            const firstRoom = parseRoomCode(sortedRooms[0].code);
            const isCorrect = firstRoom.floor === 1 && firstRoom.zone === 'E';
            sortingTests.innerHTML += `
                <div class="test-result ${isCorrect ? 'pass' : 'fail'}">
                    ${isCorrect ? '✅' : '❌'} Spatial sorting ${isCorrect ? 'correctly' : 'incorrectly'} 
                    prioritized Floor 1, Zone E rooms
                </div>
            `;
        }

        // Run tests when page loads
        window.onload = runTests;
    </script>
</body>
</html>
