# Intelligent Spatial Room Sorting Implementation

## Overview

This implementation adds intelligent room name sorting to the room assignment dropdown based on spatial proximity between the annotation's centroid and estimated room locations derived from room codes.

## Features Implemented

### 1. **Room Code Extraction**
- Parses room names to extract location codes after the first space
- Pattern: `"ROOM_TYPE XX.Y.ZZ"` → extracts `"XX.Y.ZZ"`
- Examples:
  - `"BIOWASTE 01.E.28"` → `"01.E.28"`
  - `"STORAGE 02.W.15"` → `"02.W.15"`
  - `"OFFICE 03.A.05"` → `"03.A.05"`

### 2. **Spatial Coordinate Estimation**
- Converts room codes into estimated coordinates using heuristic mapping
- Format: `"FLOOR.ZONE.ROOM"` where:
  - **FLOOR**: Floor number (01, 02, 03, etc.)
  - **ZONE**: Zone letter (A-I mapped to grid positions)
  - **ROOM**: Room number within zone

### 3. **Zone Mapping System**
```
A(0,0)  B(1,0)  C(2,0)    Northwest → Northeast
D(0,1)  E(1,1)  F(2,1)    West → Center → East  
G(0,2)  H(1,2)  I(2,2)    Southwest → Southeast
```

### 4. **Annotation Centroid Calculation**
- **Rectangles**: Center point = (x + width/2, y + height/2)
- **Polygons**: Average of all vertex coordinates
- Converts canvas coordinates to PDF coordinates for consistency

### 5. **Distance-Based Sorting**
- Calculates Euclidean distance between annotation centroid and estimated room coordinates
- Prioritizes rooms with valid location codes
- Sorts by proximity (closest first)
- Falls back to alphabetical sorting for rooms without codes

### 6. **Visual Indicators**
- **Proximity Indicator**: 📍 icon for nearby rooms (< 500 units)
- **Distance Badge**: "Very Close" badge for very nearby rooms (< 200 units)
- **Spatial Sorting Header**: Shows when spatial sorting is active
- **Color Coding**: Blue highlight for nearby rooms

## Technical Implementation

### Files Modified

1. **`src/components/RoomNameDropdown.jsx`**
   - Added spatial sorting logic
   - Added visual proximity indicators
   - Added debugging information

2. **`src/App.jsx`**
   - Passed `pendingAnnotation` and `canvasToPdfCoordinates` props to dropdown

3. **`src/App.css`**
   - Added styling for spatial sorting indicators
   - Added proximity highlighting styles

### Key Functions

#### `extractRoomCode(roomName)`
Extracts location code from room name using regex pattern.

#### `parseRoomCode(roomCode)`
Parses room code into floor, zone, and room number components.

#### `calculateAnnotationCentroid(annotation)`
Calculates the geometric center of an annotation in PDF coordinates.

#### `estimateRoomCoordinates(roomCode)`
Estimates room coordinates based on parsed room code components.

#### `sortRoomsByProximity(rooms, annotationCentroid)`
Sorts room list by distance from annotation centroid.

## Usage

### Automatic Activation
- Spatial sorting activates automatically when:
  1. A new annotation is created (rectangle or polygon)
  2. Room assignment dropdown appears
  3. CSV contains rooms with valid location codes
  4. Coordinate conversion is available

### Visual Feedback
- **Header Message**: "📍 Sorted by proximity to annotation"
- **Nearby Rooms**: Blue background with location pin icon
- **Very Close Rooms**: Green "Very Close" badge
- **Tooltips**: Show room code and distance information

### Debug Information
Console logs provide detailed spatial sorting information:
```javascript
🎯 Spatial Room Sorting Debug: {
  annotationCentroid: {x: 150, y: 200},
  annotationType: "rectangle",
  topRooms: [
    {name: "OFFICE 01.E.15", code: "01.E.15", distance: 45, coords: {x: 150, y: 1000}},
    {name: "STORAGE 01.E.20", code: "01.E.20", distance: 95, coords: {x: 200, y: 1000}}
  ]
}
```

## Testing

### Test Scenarios

1. **Basic Spatial Sorting**
   - Create annotation near estimated location of room "01.E.28"
   - Verify rooms with "01.E.XX" codes appear first
   - Check visual indicators are displayed

2. **Cross-Floor Proximity**
   - Create annotation on floor plan
   - Verify rooms from same floor (01.X.XX) rank higher than other floors

3. **Zone Proximity**
   - Create annotation in estimated zone E area
   - Verify zone E rooms (XX.E.XX) rank higher than other zones

4. **Fallback Behavior**
   - Test with rooms without valid codes
   - Verify alphabetical sorting for non-coded rooms
   - Ensure coded rooms always rank above non-coded rooms

### Expected Results

- ✅ Rooms with matching floor codes appear first
- ✅ Rooms with matching zone codes rank higher within same floor
- ✅ Distance calculation provides logical ordering
- ✅ Visual indicators help identify nearby rooms
- ✅ Fallback to alphabetical sorting works for edge cases

## Benefits

1. **Improved User Experience**: Most relevant rooms appear first
2. **Faster Room Assignment**: Reduces scrolling and searching
3. **Spatial Awareness**: Leverages location codes for intelligent suggestions
4. **Visual Feedback**: Clear indicators for proximity and relevance
5. **Robust Fallbacks**: Handles edge cases gracefully

## Limitations

1. **Heuristic Coordinates**: Uses estimated rather than actual room coordinates
2. **Code Pattern Dependency**: Requires specific room naming convention
3. **Zone Mapping**: Limited to 9-zone grid system (A-I)
4. **Distance Units**: Arbitrary units rather than real-world measurements

## Future Enhancements

1. **Real Coordinate Integration**: Support for actual room coordinates in CSV
2. **Custom Zone Mapping**: Configurable zone layouts
3. **Distance Units**: Convert to real-world measurements (meters/feet)
4. **Machine Learning**: Learn from user selections to improve sorting
5. **Multi-Building Support**: Handle complex building layouts
