/* Base font size scaling for different screen sizes */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-size: clamp(14px, 1.2vw, 18px);
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f8f9fa;
}

#root {
  width: 100%;
  height: 100%;
}

.app {
  position: relative;
  height: 100vh;
  width: 100vw;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  overflow: hidden;
  font-size: 1rem;
  box-sizing: border-box;
  background-color: #f8f9fa;
}

.toolbar {
  position: fixed;
  top: min(20px, 1.5vh);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: clamp(8px, 1.5vw, 20px);
  padding: clamp(8px, 1vh, 16px) clamp(12px, 2vw, 24px);
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: clamp(15px, 2vw, 30px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  flex-wrap: nowrap;
  max-width: min(95vw, 1400px);
  overflow: visible; /* Allow dropdowns to overflow outside toolbar */
  animation: slideDown 0.3s ease-out;
  font-size: clamp(11px, 1.2vw, 14px);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.toolbar button {
  padding: clamp(6px, 0.8vh, 12px) clamp(8px, 1.2vw, 16px);
  border: 1px solid rgba(0, 0, 0, 0.2);
  background-color: rgba(255, 255, 255, 0.95);
  color: #333 !important;
  border-radius: clamp(12px, 1.5vw, 24px);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: clamp(11px, 1.1vw, 14px);
  font-weight: 500;
  white-space: nowrap;
  min-width: auto;
}

.toolbar button:hover {
  background-color: rgba(240, 240, 240, 0.95);
  color: #222 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toolbar button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  color: #666 !important;
  background-color: rgba(240, 240, 240, 0.8) !important;
}

.toolbar button.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.toolbar .icon-button {
  width: clamp(32px, 3vw, 48px);
  height: clamp(32px, 3vw, 48px);
  padding: clamp(6px, 0.8vw, 12px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: clamp(14px, 1.8vw, 24px);
  position: relative;
  overflow: hidden;
}

.toolbar .icon-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.toolbar .icon-button:hover::before {
  transform: translateX(100%);
}

.toolbar .icon-button.active {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white !important;
  border-color: #007bff;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: clamp(6px, 1vw, 12px);
  padding: 0 clamp(6px, 1vw, 12px);
}

.toolbar-section:not(:last-child) {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.toolbar-section span {
  font-size: clamp(10px, 1vw, 13px);
  color: #666;
  white-space: nowrap;
  font-weight: 600;
  min-width: 45px;
  text-align: center;
}

.pdf-dropdown-container {
  position: relative;
  z-index: 1100; /* Ensure container has proper stacking context */
}

.pdf-dropdown {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #ddd;
  border-radius: clamp(6px, 0.8vw, 12px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1200; /* Higher z-index to ensure it appears above toolbar */
  min-width: clamp(180px, 20vw, 250px);
  max-width: clamp(250px, 30vw, 400px);
  /* Remove max-height and overflow-y to allow natural overflow */
  margin-top: clamp(3px, 0.5vh, 8px);
  font-size: clamp(11px, 1vw, 13px);
}

.pdf-dropdown-item {
  padding: clamp(6px, 0.8vw, 10px) clamp(8px, 1.2vw, 15px);
  cursor: pointer;
  border-bottom: 1px solid #eee;
  font-size: clamp(11px, 1vw, 13px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333;
}

.pdf-dropdown-item:hover {
  background-color: #f5f5f5;
}

.pdf-dropdown-item.active {
  background-color: #e3f2fd;
  color: #1976d2;
  font-weight: bold;
}

.pdf-dropdown-item:last-child {
  border-bottom: none;
}

.main-content {
  display: flex;
  height: 100vh;
  padding-top: clamp(70px, 8vh, 120px); /* Space for floating toolbar */
  width: 100vw;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
}

.canvas-container {
  flex: 1;
  overflow: hidden;
  padding: clamp(15px, 2vw, 30px);
  background-color: #f8f9fa;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - clamp(120px, 15vh, 200px));
  position: relative;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
}



.annotations-list {
  width: clamp(280px, 25vw, 400px);
  padding: clamp(15px, 2vw, 25px);
  background-color: white;
  border-left: 1px solid #ddd;
  overflow-y: auto;
  font-size: clamp(12px, 1.1vw, 14px);
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.annotations-list h3 {
  margin: 0 0 clamp(12px, 1.5vw, 20px) 0;
  color: #333;
  font-size: clamp(14px, 1.3vw, 18px);
}

.annotation-items {
  display: flex;
  flex-direction: column;
  gap: clamp(8px, 1vw, 12px);
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

.annotation-item {
  padding: clamp(8px, 1vw, 12px);
  border: 1px solid #ddd;
  border-radius: clamp(4px, 0.5vw, 8px);
  background-color: #f9f9f9;
  cursor: pointer;
  transition: all 0.2s ease;
}

.annotation-item:hover {
  background-color: #e9e9e9;
  border-color: #bbb;
}

.annotation-label {
  font-weight: bold;
  color: #333 !important;
  margin-bottom: clamp(4px, 0.5vw, 6px);
  cursor: pointer;
  padding: clamp(2px, 0.3vw, 4px);
  border-radius: clamp(2px, 0.3vw, 4px);
  transition: background-color 0.2s ease;
}

.annotation-label:hover {
  background-color: #f0f0f0;
}

.annotation-label-input {
  width: 100%;
  padding: clamp(2px, 0.3vw, 4px);
  border: 1px solid #ccc;
  border-radius: clamp(2px, 0.3vw, 4px);
  font-size: inherit;
  font-weight: bold;
  background-color: white;
  color: #333 !important;
  outline: none;
}

.annotation-label-input:focus {
  border-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.annotation-details {
  font-size: clamp(10px, 1vw, 12px);
  color: #666 !important;
}

.overlap-warning {
  background-color: #fff3cd !important;
  border-color: #ffc107 !important;
  color: #856404 !important;
}

.overlap-warning .annotation-label {
  color: #856404 !important;
}

.overlap-warning .annotation-details {
  color: #856404 !important;
}

.annotation-item.selected {
  background-color: #e8f5e8;
  border-color: #4caf50;
  box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
}

.annotation-item span {
  display: block;
  margin-bottom: clamp(6px, 0.8vw, 10px);
  font-size: clamp(12px, 1.1vw, 14px);
  color: #555;
}

.annotation-controls {
  display: flex;
  gap: clamp(4px, 0.5vw, 8px);
}

.annotation-controls button {
  padding: clamp(3px, 0.5vw, 6px) clamp(6px, 0.8vw, 10px);
  font-size: clamp(10px, 1vw, 12px);
  border: 1px solid #ccc;
  background-color: white !important;
  color: #333 !important;
  border-radius: clamp(3px, 0.4vw, 6px);
  cursor: pointer;
  transition: all 0.2s ease;
}

.annotation-controls button:hover {
  background-color: #f0f0f0 !important;
  border-color: #999;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.annotation-control-button {
  padding: clamp(3px, 0.5vw, 6px) clamp(6px, 0.8vw, 10px) !important;
  font-size: clamp(10px, 1vw, 12px) !important;
  border: 1px solid #ccc !important;
  background-color: white !important;
  color: #333 !important;
  border-radius: clamp(3px, 0.4vw, 6px) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.annotation-control-button:hover {
  background-color: #f0f0f0 !important;
  border-color: #999 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  color: #222 !important;
}

.selected-annotation-info {
  display: flex;
  align-items: center;
  gap: clamp(6px, 1vw, 12px);
  padding: clamp(4px, 0.8vw, 8px) clamp(8px, 1.2vw, 12px);
  background-color: rgba(232, 245, 232, 0.95);
  border: 1px solid #4caf50;
  border-radius: clamp(15px, 2vw, 20px);
  font-size: clamp(11px, 1vw, 13px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selected-annotation-info span {
  font-weight: bold;
  color: #2e7d32 !important;
  font-size: clamp(11px, 1vw, 13px);
}

.selected-annotation-info .icon-button {
  background-color: rgba(255, 255, 255, 0.9) !important;
  color: #333 !important;
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.annotation-help {
  margin-top: 20px;
  padding: 15px;
  background-color: #f0f8ff;
  border: 1px solid #b3d9ff;
  border-radius: 4px;
  font-size: 12px;
}

.annotation-help p {
  margin: 0 0 10px 0;
  font-weight: bold;
  color: #1976d2;
}

.annotation-help ul {
  margin: 0;
  padding-left: 20px;
}

.annotation-help li {
  margin-bottom: 5px;
  color: #555;
}

.canvas-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-height: 400px;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.zoom-indicator {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: bold;
  z-index: 100;
  animation: zoomFade 1s ease-out;
  pointer-events: none;
}

.annotation-counter {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: bold;
  z-index: 100;
  pointer-events: none;
}

@keyframes zoomFade {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  20% {
    opacity: 1;
    transform: scale(1);
  }
  80% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

.pdf-canvas {
  border: 2px solid #e1e5e9;
  background-color: white;
  cursor: crosshair;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  user-select: none;
  max-width: calc(100vw - clamp(40px, 8vw, 120px));
  max-height: calc(100vh - clamp(120px, 15vh, 200px));
  object-fit: contain;
  transition: transform 0.1s ease-out;
  display: block;
  margin: 0;
  border-radius: 8px;
  position: relative;
  box-sizing: border-box;
}

.pdf-canvas.hand-cursor {
  cursor: grab;
}

.pdf-canvas.select-cursor {
  cursor: pointer;
}

/* Welcome Screen Styles */
.welcome-screen {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  min-height: calc(100vh - 120px);
  padding: 2rem;
  box-sizing: border-box;
  overflow-y: auto;
}

.welcome-content {
  max-width: 1000px;
  width: 100%;
  text-align: center;
  color: #333;
  margin: 0 auto;
}

.welcome-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.welcome-content h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-weight: 600;
}

.welcome-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 3rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature-item {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.feature-item h3 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.feature-item p {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

.getting-started {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  text-align: left;
}

.getting-started h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  text-align: center;
}

.getting-started ol {
  max-width: 500px;
  margin: 0 auto;
}

.getting-started li {
  margin-bottom: 0.5rem;
  color: #555;
}

.keyboard-shortcuts {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.keyboard-shortcuts h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
  font-size: 0.9rem;
}

.shortcuts-grid div {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #555;
}

kbd {
  background: #f1f3f4;
  border: 1px solid #dadce0;
  border-radius: 4px;
  padding: 2px 6px;
  font-family: monospace;
  font-size: 0.8rem;
  color: #333;
}

/* Mobile responsive design */
@media (max-width: 768px) {
  .welcome-screen {
    padding: 1rem;
    align-items: flex-start;
  }

  .welcome-content {
    padding: 0;
    max-width: 100%;
  }

  .welcome-content h1 {
    font-size: 2rem;
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .shortcuts-grid {
    grid-template-columns: 1fr 1fr;
  }

  .getting-started,
  .keyboard-shortcuts {
    padding: 1.5rem;
  }

  .canvas-container {
    padding: clamp(10px, 2vw, 20px);
  }

  .main-content {
    width: 100vw;
    overflow-x: hidden;
  }
}

@media (max-width: 480px) {
  .welcome-icon {
    font-size: 3rem;
  }

  .welcome-content h1 {
    font-size: 1.8rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .shortcuts-grid {
    grid-template-columns: 1fr;
  }
}

.pdf-canvas.hand-cursor:active {
  cursor: grabbing;
}

/* Responsive design for different screen sizes */

/* Large screens (1920px and above) */
@media (min-width: 1920px) {
  .toolbar {
    gap: 25px;
    padding: 18px 30px;
    border-radius: 35px;
  }

  .toolbar .icon-button {
    width: 56px;
    height: 56px;
    font-size: 28px;
  }

  .toolbar button {
    font-size: 16px;
    padding: 12px 20px;
  }

  .toolbar-section span {
    font-size: 15px;
  }

  .annotations-list {
    width: 450px;
    padding: 30px;
    font-size: 16px;
  }

  .annotations-list h3 {
    font-size: 20px;
  }
}

/* Medium-large screens (1200px to 1919px) */
@media (min-width: 1200px) and (max-width: 1919px) {
  .toolbar {
    gap: 20px;
    padding: 15px 25px;
  }

  .toolbar .icon-button {
    width: 44px;
    height: 44px;
    font-size: 22px;
  }

  .annotations-list {
    width: 350px;
    padding: 25px;
  }
}

/* Tablet screens (768px to 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
  .toolbar {
    gap: 15px;
    padding: 12px 20px;
  }

  .toolbar .icon-button {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .annotations-list {
    width: 300px;
    padding: 20px;
  }
}

/* Mobile screens (up to 767px) */
@media (max-width: 767px) {
  .toolbar {
    top: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    max-width: none;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 20px;
    flex-wrap: wrap;
  }

  .toolbar-section {
    gap: 4px;
    padding: 0 4px;
  }

  .toolbar-section span {
    font-size: 10px;
  }

  .toolbar .icon-button {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .main-content {
    flex-direction: column;
    padding-top: 80px;
  }

  .canvas-container {
    padding: 10px;
    align-items: flex-start;
  }

  .canvas-wrapper {
    min-height: 300px;
  }

  .annotations-list {
    width: 100%;
    max-height: 200px;
    padding: 15px;
    font-size: 12px;
  }

  .annotations-list h3 {
    font-size: 14px;
  }

  .selected-annotation-info {
    gap: 4px;
    padding: 2px 6px;
  }

  .selected-annotation-info span {
    font-size: 10px;
  }

  .pdf-dropdown {
    min-width: 150px;
    max-width: 250px;
    font-size: 11px;
  }

  .pdf-dropdown-item {
    padding: 6px 8px;
    font-size: 11px;
  }
}

/* Ultra-wide screens (2560px and above) */
@media (min-width: 2560px) {
  .toolbar {
    gap: 30px;
    padding: 20px 35px;
    border-radius: 40px;
  }

  .toolbar .icon-button {
    width: 64px;
    height: 64px;
    font-size: 32px;
  }

  .toolbar button {
    font-size: 18px;
    padding: 14px 24px;
  }

  .toolbar-section span {
    font-size: 16px;
  }

  .annotations-list {
    width: 500px;
    padding: 35px;
    font-size: 18px;
  }

  .annotations-list h3 {
    font-size: 22px;
  }

  .annotation-item {
    padding: 15px;
  }

  .annotation-item span {
    font-size: 16px;
  }
}

/* Room Name Dropdown Styles */
.room-name-dropdown {
  background: white;
  border: 2px solid #4caf50;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 300px;
  max-width: 400px;
  max-height: 400px;
  display: flex;
  flex-direction: column;
}

.room-dropdown-header {
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.room-dropdown-header h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
  font-weight: bold;
}

.room-search-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 12px;
  color: #333 !important;
  background-color: white;
  outline: none;
}

.room-search-input:focus {
  border-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.room-dropdown-list {
  flex: 1;
  overflow-y: auto;
  max-height: 250px;
}

.room-dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 12px;
  color: #333 !important;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.room-dropdown-item:hover,
.room-dropdown-item.selected {
  background-color: #e8f5e8;
}

.room-dropdown-item.no-results {
  color: #666;
  font-style: italic;
  cursor: default;
}

.room-dropdown-item.no-results:hover {
  background-color: transparent;
}

.room-dropdown-footer {
  padding: 8px 12px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.room-dropdown-footer button {
  padding: 4px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.cancel-button:hover {
  background-color: #f5f5f5;
  border-color: #999;
}

.cancel-button {
  color: black !important;
}

.select-button {
  background-color: #4caf50 !important;
  color: white !important;
  border-color: #4caf50 !important;
}

.select-button:hover {
  background-color: #45a049 !important;
  border-color: #45a049 !important;
}

/* CSV Upload Button Styles */
.csv-upload-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.csv-status {
  font-size: 11px;
  color: #666;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.csv-status.loaded {
  color: #4caf50;
  font-weight: bold;
}

/* Hierarchical Room Filter Styles */
.hierarchical-filter-overlay {
  background: white;
  border: 2px solid #4caf50;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 400px;
  max-width: 600px;
  max-height: 500px;
  display: flex;
  flex-direction: column;
}

.hierarchical-room-filter {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.filter-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: bold;
}

.clear-filters-btn {
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 11px;
  color: #666;
  transition: all 0.2s ease;
}

.clear-filters-btn:hover {
  background-color: #f0f0f0;
  border-color: #999;
}

.filter-levels {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-level {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-level-label {
  font-size: 12px;
  font-weight: bold;
  color: #555;
  margin-bottom: 4px;
}

.filter-level-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  background: white;
  color: #333;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.filter-level-select:focus {
  outline: none;
  border-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.selected-path {
  padding: 8px 12px;
  background-color: #e8f5e8;
  border-radius: 4px;
  font-size: 12px;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.filtered-rooms {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filtered-rooms h5 {
  margin: 0;
  font-size: 13px;
  color: #333;
  font-weight: bold;
}

.rooms-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
}

.room-item {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background: white;
  color: #333;
  cursor: pointer;
  font-size: 12px;
  text-align: left;
  transition: all 0.2s ease;
}

.room-item:hover {
  background-color: #e8f5e8;
  border-color: #4caf50;
}

.no-rooms-message {
  padding: 12px;
  text-align: center;
  color: #666;
  font-style: italic;
  font-size: 12px;
}

.filter-message {
  padding: 16px;
  text-align: center;
  color: #666;
  font-size: 12px;
}

.filter-actions {
  padding: 12px 16px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  display: flex;
  justify-content: flex-end;
}

.filter-actions .cancel-button {
  padding: 6px 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.filter-actions .cancel-button:hover {
  background-color: #f0f0f0;
}

.filter-actions .clear-button {
  padding: 6px 12px;
  border: 1px solid #ff9800;
  background: white;
  color: #ff9800;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.filter-actions .clear-button:hover {
  background-color: #fff3e0;
}

/* Responsive styles for hierarchical filter */
@media (max-width: 768px) {
  .hierarchical-filter-overlay {
    min-width: 300px;
    max-width: 90vw;
    max-height: 80vh;
  }

  .hierarchical-room-filter {
    padding: 12px;
    gap: 12px;
  }

  .filter-header h4 {
    font-size: 14px;
  }

  .filter-level-select {
    padding: 6px 8px;
    font-size: 11px;
  }

  .room-item {
    padding: 6px 8px;
    font-size: 11px;
  }

  .rooms-list {
    max-height: 150px;
  }
}

@media (max-width: 480px) {
  .hierarchical-filter-overlay {
    min-width: 280px;
    max-width: 95vw;
  }

  .filter-levels {
    gap: 8px;
  }

  .filter-level-label {
    font-size: 11px;
  }

  .selected-path {
    font-size: 11px;
    padding: 6px 8px;
  }
}

/* Pre-filter modal styles */
.pre-filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.pre-filter-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
}

.pre-filter-header {
  padding: 20px 20px 10px 20px;
  border-bottom: 1px solid #e0e0e0;
  text-align: center;
}

.pre-filter-header h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
}

.pre-filter-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.pre-filter .hierarchical-room-filter {
  padding: 20px;
  border: none;
  box-shadow: none;
}

.pre-filter-actions {
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.pre-filter-actions .skip-button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  color: #666;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.pre-filter-actions .skip-button:hover {
  background-color: #f5f5f5;
}

.pre-filter-actions .apply-button {
  padding: 8px 16px;
  border: none;
  background: #4caf50;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.pre-filter-actions .apply-button:hover {
  background-color: #45a049;
}

/* Toolbar filter control styles */
.filter-control-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 10px;
  padding-left: 10px;
  border-left: 1px solid #ddd;
}

.filter-status {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #e8f5e8;
  border: 1px solid #4caf50;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
}

.filter-path {
  color: #2e7d32;
  font-weight: 500;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.clear-filter-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0;
  font-size: 10px;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
}

.clear-filter-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
}

/* Active filter button styling */
.icon-button.active {
  background-color: #4caf50;
  color: white;
}

.icon-button.active:hover {
  background-color: #45a049;
}

/* Enhanced hierarchical filter styles */
.filter-summary {
  margin: 15px 0;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #4caf50;
}

.selected-path {
  color: #2e7d32;
  font-weight: 500;
  margin-bottom: 8px;
}

.filter-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.rooms-count {
  font-weight: 500;
  color: #4caf50;
}

.continue-hint {
  font-style: italic;
  color: #888;
}

.filter-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 15px 0;
  padding: 12px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
}

.apply-filter-btn {
  padding: 10px 16px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 13px;
  transition: background-color 0.2s ease;
}

.apply-filter-btn:hover {
  background-color: #45a049;
}

.continue-text {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.filtered-rooms h5 {
  margin: 15px 0 8px 0;
  color: #333;
  font-size: 13px;
}

.rooms-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  max-height: 120px;
  overflow-y: auto;
}

.room-item {
  padding: 6px 10px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  color: #333;
  transition: all 0.2s ease;
}

.room-item:hover {
  background-color: #e8f5e8;
  border-color: #4caf50;
  color: #2e7d32;
}

.more-rooms-hint {
  padding: 6px 10px;
  color: #666;
  font-size: 11px;
  font-style: italic;
  background-color: #f9f9f9;
  border: 1px dashed #ccc;
  border-radius: 3px;
}

/* Custom room input styles */
.input-mode-toggle {
  display: flex;
  gap: 4px;
  margin-bottom: 8px;
}

.mode-toggle-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  background: white;
  color: #666;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.mode-toggle-btn.active {
  background-color: #4caf50;
  color: white;
  border-color: #4caf50;
}

.mode-toggle-btn:hover:not(.active) {
  background-color: #f5f5f5;
  border-color: #999;
}

.room-custom-input {
  width: 100%;
  padding: 8px;
  border: 2px solid #4caf50;
  border-radius: 4px;
  font-size: 12px;
  background-color: #f8fff8;
}

.room-custom-input:focus {
  outline: none;
  border-color: #45a049;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.custom-room-preview {
  padding: 15px;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fff8;
  border: 1px solid #e8f5e8;
  border-radius: 4px;
}

.custom-room-display {
  text-align: center;
}

.custom-room-label {
  display: block;
  font-size: 11px;
  color: #666;
  margin-bottom: 4px;
}

.custom-room-name {
  font-size: 14px;
  font-weight: 500;
  color: #2e7d32;
  background-color: white;
  padding: 4px 8px;
  border-radius: 3px;
  border: 1px solid #4caf50;
}

.custom-room-hint {
  font-size: 12px;
  color: #888;
  text-align: center;
  font-style: italic;
}

.switch-to-custom-btn {
  display: block;
  margin: 8px auto 0;
  padding: 4px 8px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
}

.switch-to-custom-btn:hover {
  background-color: #45a049;
}

.add-custom-button {
  padding: 6px 12px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
}

.add-custom-button:hover:not(:disabled) {
  background-color: #45a049;
}

.add-custom-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.custom-mode-button {
  padding: 6px 10px;
  background-color: #2196f3;
  color: black;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  margin-left: 8px;
}

.custom-mode-button:hover {
  background-color: #1976d2;
}

/* Interactive Hierarchical Filter Styles */
.interactive-hierarchical-filter {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  max-height: 600px;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.filter-header h4 {
  margin: 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 3px;
}

.close-button:hover {
  background-color: #f0f0f0;
  color: #333;
}

.filter-instructions {
  padding: 8px 16px;
  font-size: 11px;
  color: #666;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eee;
  font-style: italic;
}

.current-selection {
  padding: 12px 16px;
  background-color: #f0f8ff;
  border-bottom: 1px solid #e0e8f0;
}

.selected-path {
  color: #2e7d32;
  font-weight: 500;
  margin-bottom: 6px;
  font-size: 12px;
}

.selection-stats {
  margin-bottom: 8px;
}

.rooms-count {
  font-size: 11px;
  color: #4caf50;
  font-weight: 500;
}

.apply-filter-btn {
  padding: 6px 12px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  font-weight: 500;
}

.apply-filter-btn:hover {
  background-color: #45a049;
}

.tree-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
  max-height: 300px;
}

.tree-node {
  user-select: none;
}

.tree-item {
  margin: 1px 0;
}

.tree-item.selected .folder-button {
  background-color: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.tree-item-content {
  display: flex;
  align-items: center;
  padding: 2px 8px;
}

.expand-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px 4px;
  margin-right: 4px;
  color: #666;
  font-size: 10px;
  width: 16px;
  text-align: center;
}

.expand-button:hover {
  background-color: #f0f0f0;
  border-radius: 2px;
}

.folder-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 6px;
  border-radius: 4px;
  flex: 1;
  text-align: left;
  font-size: 12px;
}

.folder-button:hover {
  background-color: #f5f5f5;
}

.folder-icon {
  font-size: 14px;
}

.folder-name {
  color: #333;
  font-weight: 500;
}

.room-count {
  color: #666;
  font-size: 10px;
  margin-left: auto;
}

.tree-children {
  margin-left: 0;
}

.room-selection-section {
  padding: 12px 16px;
  border-top: 1px solid #eee;
  background-color: #fafafa;
}

.room-selection-section h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #333;
}

.rooms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 4px;
  max-height: 120px;
  overflow-y: auto;
}

.room-button {
  padding: 4px 6px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
  color: #333;
  text-align: center;
}

.room-button:hover {
  background-color: #e8f5e8;
  border-color: #4caf50;
  color: #2e7d32;
}

.more-rooms-indicator {
  padding: 4px 6px;
  color: #666;
  font-size: 10px;
  font-style: italic;
  background-color: #f9f9f9;
  border: 1px dashed #ccc;
  border-radius: 3px;
  text-align: center;
}

.no-data-message {
  padding: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
}

/* Landing Page Styles */
.landing-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 15px;
  overflow: hidden;
}

.landing-content {
  max-width: 1100px;
  width: 100%;
  height: calc(100vh - 30px);
  background: white;
  border-radius: 15px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  padding: 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.landing-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
  flex: 1;
  text-align: left;
  align-items: start;
  overflow: hidden;
}

.landing-header h1 {
  font-size: 2rem;
  color: #333;
  margin-bottom: 8px;
  font-weight: 700;
}

.landing-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.4;
}

.upload-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: 100%;
  overflow-y: auto;
}

.instructions-sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  overflow-y: auto;
}

.upload-card {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.upload-card:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.upload-card.secondary {
  background: #f9f9f9;
  border-color: #e9ecef;
}

.upload-card.secondary:hover {
  border-color: #28a745;
  background: #f0fff4;
}

.upload-icon {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.upload-card h2 {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 8px;
}

.upload-card h3 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 8px;
}

.upload-card p {
  color: #666;
  margin-bottom: 15px;
  line-height: 1.4;
  font-size: 0.95rem;
}

.upload-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.upload-button:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.upload-button.secondary {
  background: #28a745;
}

.upload-button.secondary:hover {
  background: #218838;
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.upload-hint {
  font-size: 0.9rem;
  color: #999;
  margin: 0;
}

.divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: #dee2e6;
}

.divider span {
  padding: 0 20px;
  color: #999;
  font-size: 0.9rem;
  background: white;
}

.csv-status {
  background: #d4edda;
  color: #155724;
  padding: 10px 15px;
  border-radius: 8px;
  margin-top: 15px;
  font-weight: 500;
}

.features-section {
  margin-top: 30px;
}

.features-section h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 25px;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.feature-icon {
  font-size: 1.5rem;
  margin-top: 5px;
}

.feature-item h4 {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 5px;
}

.feature-item p {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.4;
  margin: 0;
}

.instructions-section {
  text-align: left;
}

.instructions-section h3 {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.instructions-list {
  list-style: none;
  counter-reset: step-counter;
  padding: 0;
}

.instructions-list li {
  counter-increment: step-counter;
  margin-bottom: 12px;
  padding-left: 35px;
  position: relative;
  line-height: 1.4;
  color: #555;
  font-size: 0.95rem;
}

.instructions-list li::before {
  content: counter(step-counter);
  position: absolute;
  left: 0;
  top: 0;
  background: #667eea;
  color: white;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.8rem;
}

/* Navbar Instructions Styles */
.navbar-instructions {
  text-align: left;
  background: #f8f9fa;
  padding: 18px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.navbar-instructions h3 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
}

.navbar-intro {
  color: #666;
  margin-bottom: 18px;
  text-align: center;
  font-size: 0.95rem;
  line-height: 1.4;
}

.navbar-tools {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
  margin-bottom: 15px;
}

.tool-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 10px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
  font-size: 0.85rem;
}

.tool-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.tool-icon {
  font-size: 1.1rem;
  margin-top: 1px;
  flex-shrink: 0;
}

.tool-item strong {
  color: #333;
  font-weight: 600;
}

.tool-item div {
  color: #555;
  font-size: 0.95rem;
  line-height: 1.4;
}

.navbar-tip {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  font-size: 1rem;
  line-height: 1.5;
}

/* Toast Styles */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  max-width: 400px;
  min-width: 300px;
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  transform: translateX(100%);
  opacity: 0;
}

.toast-show {
  transform: translateX(0);
  opacity: 1;
}

.toast-hide {
  transform: translateX(100%);
  opacity: 0;
}

.toast-loading {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toast-success {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toast-error {
  background: linear-gradient(135deg, #dc3545, #fd7e14);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toast-warning {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.toast-info {
  background: linear-gradient(135deg, #17a2b8, #6f42c1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
}

.toast-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  font-weight: 500;
  line-height: 1.4;
}

.toast-spinner {
  flex-shrink: 0;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.toast-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.toast-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .landing-page {
    padding: 10px;
  }

  .landing-content {
    height: calc(100vh - 20px);
    padding: 15px;
  }

  .landing-header h1 {
    font-size: 1.8rem;
    margin-bottom: 5px;
  }

  .landing-subtitle {
    font-size: 1rem;
    margin-bottom: 15px;
  }

  .landing-main {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .upload-card {
    padding: 15px;
  }

  .navbar-instructions {
    padding: 15px;
  }
}

  .landing-header h1 {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .upload-card {
    padding: 20px;
  }

  .upload-button {
    padding: 12px 25px;
    font-size: 1rem;
  }

  .toast {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
  }

  .navbar-tools {
    grid-template-columns: 1fr;
  }

  .tool-item {
    padding: 12px;
  }
