import React, { useState, useEffect, useRef } from 'react'

const RoomNameDropdown = ({
  roomNames,
  position,
  onSelectRoom,
  onCancel,
  isVisible,
  currentAnnotations = [],
  pendingAnnotation = null, // New prop for spatial sorting
  canvasToPdfCoordinates = null // New prop for coordinate conversion
}) => {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const [customRoomName, setCustomRoomName] = useState('')
  const [showCustomInput, setShowCustomInput] = useState(false)
  const dropdownRef = useRef(null)
  const searchInputRef = useRef(null)
  const customInputRef = useRef(null)

  // Get used room names from current annotations
  const usedRoomNames = new Set(
    currentAnnotations
      .filter(annotation => annotation.roomName || annotation.label)
      .map(annotation => annotation.roomName || annotation.label)
  )

  // Extract room code from room name (e.g., "BIOWASTE 01.E.28" → "01.E.28")
  const extractRoomCode = (roomName) => {
    const match = roomName.match(/\s+([0-9]+\.[A-Z]+\.[0-9]+)$/)
    return match ? match[1] : null
  }

  // Parse room code into spatial components (e.g., "01.E.28" → {floor: 1, zone: 'E', room: 28})
  const parseRoomCode = (roomCode) => {
    if (!roomCode) return null
    const parts = roomCode.split('.')
    if (parts.length !== 3) return null

    return {
      floor: parseInt(parts[0], 10) || 0,
      zone: parts[1],
      room: parseInt(parts[2], 10) || 0
    }
  }

  // Calculate annotation centroid in PDF coordinates
  const calculateAnnotationCentroid = (annotation) => {
    if (!annotation || !canvasToPdfCoordinates) return null

    if (annotation.type === 'rectangle') {
      const centerX = annotation.x + annotation.width / 2
      const centerY = annotation.y + annotation.height / 2
      return canvasToPdfCoordinates(centerX, centerY)
    } else if (annotation.type === 'polygon' && annotation.points && annotation.points.length > 0) {
      // Calculate polygon centroid
      const sumX = annotation.points.reduce((sum, point) => sum + point.x, 0)
      const sumY = annotation.points.reduce((sum, point) => sum + point.y, 0)
      const centerX = sumX / annotation.points.length
      const centerY = sumY / annotation.points.length
      return canvasToPdfCoordinates(centerX, centerY)
    }

    return null
  }

  // Estimate room coordinates based on room code
  const estimateRoomCoordinates = (roomCode) => {
    const parsed = parseRoomCode(roomCode)
    if (!parsed) return null

    // Create a spatial mapping based on room codes
    // This is a heuristic approach since we don't have actual coordinates
    const zoneMapping = {
      'A': { x: 0, y: 0 },     // Northwest
      'B': { x: 1, y: 0 },     // Northeast
      'C': { x: 2, y: 0 },     // Far Northeast
      'D': { x: 0, y: 1 },     // West
      'E': { x: 1, y: 1 },     // Center
      'F': { x: 2, y: 1 },     // East
      'G': { x: 0, y: 2 },     // Southwest
      'H': { x: 1, y: 2 },     // South
      'I': { x: 2, y: 2 },     // Southeast
    }

    const zoneCoords = zoneMapping[parsed.zone] || { x: 1, y: 1 } // Default to center

    // Estimate coordinates based on floor, zone, and room number
    // Scale factors to spread rooms across a reasonable coordinate space
    const floorScale = 1000  // Each floor is 1000 units apart
    const zoneScale = 300    // Each zone is 300 units apart
    const roomScale = 10     // Each room is 10 units apart

    return {
      x: (zoneCoords.x * zoneScale) + (parsed.room * roomScale),
      y: (parsed.floor * floorScale) + (zoneCoords.y * zoneScale)
    }
  }

  // Calculate distance between two points
  const calculateDistance = (point1, point2) => {
    if (!point1 || !point2) return Infinity
    const dx = point1.x - point2.x
    const dy = point1.y - point2.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  // Sort rooms by spatial proximity to annotation
  const sortRoomsByProximity = (rooms, annotationCentroid) => {
    if (!annotationCentroid || !canvasToPdfCoordinates) {
      return rooms // Return unsorted if no spatial data available
    }

    const sortedRooms = rooms.map(roomName => {
      const roomCode = extractRoomCode(roomName)
      const roomCoords = estimateRoomCoordinates(roomCode)
      const distance = calculateDistance(annotationCentroid, roomCoords)

      return {
        name: roomName,
        roomCode,
        distance,
        hasValidCode: !!roomCode,
        roomCoords
      }
    }).sort((a, b) => {
      // Prioritize rooms with valid codes and closer distances
      if (a.hasValidCode && !b.hasValidCode) return -1
      if (!a.hasValidCode && b.hasValidCode) return 1
      if (a.hasValidCode && b.hasValidCode) {
        return a.distance - b.distance
      }
      // Fallback to alphabetical for rooms without codes
      return a.name.localeCompare(b.name)
    })

    // Debug logging for spatial sorting
    if (sortedRooms.length > 0 && sortedRooms[0].hasValidCode) {
      console.log('🎯 Spatial Room Sorting Debug:', {
        annotationCentroid,
        annotationType: pendingAnnotation?.type,
        topRooms: sortedRooms.slice(0, 5).map(room => ({
          name: room.name,
          code: room.roomCode,
          distance: Math.round(room.distance),
          coords: room.roomCoords
        }))
      })
    }

    return sortedRooms.map(item => item.name)
  }

  // Calculate annotation centroid for spatial sorting
  const annotationCentroid = pendingAnnotation ? calculateAnnotationCentroid(pendingAnnotation) : null

  // Filter and sort room names based on search term, usage, and spatial proximity
  const getFilteredAndSortedRoomNames = () => {
    let filtered = roomNames
      .filter(name => !usedRoomNames.has(name)) // Exclude used room names
      .filter(name => name.toLowerCase().includes(searchTerm.toLowerCase())) // Apply search filter

    // Apply spatial sorting if we have annotation data
    if (annotationCentroid && pendingAnnotation) {
      filtered = sortRoomsByProximity(filtered, annotationCentroid)
    }

    return filtered
  }

  const filteredRoomNames = getFilteredAndSortedRoomNames()

  // Reset selection when filtered list changes
  useEffect(() => {
    setSelectedIndex(0)
  }, [searchTerm])

  // Focus appropriate input when dropdown becomes visible
  useEffect(() => {
    if (isVisible) {
      if (showCustomInput && customInputRef.current) {
        customInputRef.current.focus()
      } else if (searchInputRef.current) {
        searchInputRef.current.focus()
      }
    }
  }, [isVisible, showCustomInput])

  // Handle custom room name submission
  const handleCustomRoomSubmit = () => {
    if (customRoomName.trim()) {
      onSelectRoom(customRoomName.trim())
    }
  }

  // Toggle between search and custom input modes
  const toggleCustomInput = () => {
    setShowCustomInput(!showCustomInput)
    setSearchTerm('')
    setCustomRoomName('')
    setSelectedIndex(0)
  }

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!isVisible) return

      switch (event.key) {
        case 'ArrowDown':
          if (!showCustomInput) {
            event.preventDefault()
            setSelectedIndex(prev =>
              prev < filteredRoomNames.length - 1 ? prev + 1 : prev
            )
          }
          break
        case 'ArrowUp':
          if (!showCustomInput) {
            event.preventDefault()
            setSelectedIndex(prev => prev > 0 ? prev - 1 : prev)
          }
          break
        case 'Enter':
          event.preventDefault()
          if (showCustomInput) {
            handleCustomRoomSubmit()
          } else if (filteredRoomNames.length > 0) {
            onSelectRoom(filteredRoomNames[selectedIndex])
          }
          break
        case 'Escape':
          event.preventDefault()
          onCancel()
          break
        default:
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isVisible, selectedIndex, filteredRoomNames, onSelectRoom, onCancel, showCustomInput, handleCustomRoomSubmit])

  // Handle click outside to close dropdown (with delay to prevent immediate closure)
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        onCancel()
      }
    }

    if (isVisible) {
      // Add a small delay to prevent the current mouse event from closing the dropdown
      const timer = setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside)
      }, 100)

      return () => {
        clearTimeout(timer)
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [isVisible, onCancel])

  if (!isVisible || roomNames.length === 0) {
    return null
  }

  return (
    <div
      ref={dropdownRef}
      className="room-name-dropdown"
      style={{
        position: 'fixed',
        left: position.x,
        top: position.y,
        zIndex: 1000
      }}
    >
      <div className="room-dropdown-header">
        <h4>Assign Room Name</h4>
        <div className="input-mode-toggle">
          <button
            className={`mode-toggle-btn ${!showCustomInput ? 'active' : ''}`}
            onClick={() => !showCustomInput || toggleCustomInput()}
            title="Search existing rooms"
          >
            📋 Search
          </button>
          <button
            className={`mode-toggle-btn ${showCustomInput ? 'active' : ''}`}
            onClick={() => showCustomInput || toggleCustomInput()}
            title="Type custom room name"
          >
            ✏️ Custom
          </button>
        </div>

        {showCustomInput ? (
          <input
            ref={customInputRef}
            type="text"
            placeholder="Type custom room name..."
            value={customRoomName}
            onChange={(e) => setCustomRoomName(e.target.value)}
            className="room-custom-input"
          />
        ) : (
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search room names..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="room-search-input"
          />
        )}
      </div>
      
      {showCustomInput ? (
        <div className="custom-room-preview">
          {customRoomName.trim() ? (
            <div className="custom-room-display">
              <span className="custom-room-label">Custom Room:</span>
              <span className="custom-room-name">"{customRoomName.trim()}"</span>
            </div>
          ) : (
            <div className="custom-room-hint">
              Type your custom room name above and press Enter or click Add
            </div>
          )}
        </div>
      ) : (
        <div className="room-dropdown-list">
          {annotationCentroid && pendingAnnotation && filteredRoomNames.length > 0 && (
            <div className="spatial-sorting-indicator">
              📍 Sorted by proximity to annotation
            </div>
          )}
          {filteredRoomNames.length > 0 ? (
            filteredRoomNames.map((roomName, index) => {
              const roomCode = extractRoomCode(roomName)
              const roomCoords = estimateRoomCoordinates(roomCode)
              const distance = annotationCentroid ? calculateDistance(annotationCentroid, roomCoords) : null
              const isNearby = distance !== null && distance < 500 // Consider rooms within 500 units as "nearby"

              return (
                <div
                  key={roomName}
                  className={`room-dropdown-item ${index === selectedIndex ? 'selected' : ''} ${isNearby ? 'nearby' : ''}`}
                  onClick={() => onSelectRoom(roomName)}
                  onMouseEnter={() => setSelectedIndex(index)}
                  title={roomCode ? `Room code: ${roomCode}${distance !== null ? ` • Distance: ${Math.round(distance)} units` : ''}` : roomName}
                >
                  <span className="room-name">{roomName}</span>
                  {isNearby && <span className="proximity-indicator">📍</span>}
                  {roomCode && distance !== null && distance < 200 && (
                    <span className="distance-badge">Very Close</span>
                  )}
                </div>
              )
            })
          ) : (
            <div className="room-dropdown-item no-results">
              No rooms found matching "{searchTerm}"
              <button
                className="switch-to-custom-btn text-black"
                onClick={toggleCustomInput}
              >
                Type custom name instead
              </button>
            </div>
          )}
        </div>
      )}
      
      <div className="room-dropdown-footer">
        <button onClick={onCancel} className="cancel-button">
          Cancel (Esc)
        </button>

        {showCustomInput ? (
          <button
            onClick={handleCustomRoomSubmit}
            className="add-custom-button"
            disabled={!customRoomName.trim()}
            title={customRoomName.trim() ? `Add "${customRoomName.trim()}"` : 'Enter a room name first'}
          >
            Add Custom Room (Enter)
          </button>
        ) : (
          <>
            {filteredRoomNames.length > 0 && (
              <button
                onClick={() => onSelectRoom(filteredRoomNames[selectedIndex])}
                className="select-button"
              >
                Select (Enter)
              </button>
            )}
            <button
              onClick={toggleCustomInput}
              className="custom-mode-button"
              title="Switch to custom room name input"
            >
              ✏️ Custom Name
            </button>
          </>
        )}
      </div>
    </div>
  )
}

export default RoomNameDropdown
